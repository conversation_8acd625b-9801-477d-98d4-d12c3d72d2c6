<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('账单管理')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>费用类型：</label>
                                <select name="expense_type" id="expense_type">
                                    <option value="">请选择费用类型</option>
                                </select>
                            </li>
                            <li><label>计费周期：</label><input type="text" name="billing_cycle"/></li>
                            <li>
                                <label>划账部门：</label>
                                <select name="transfer_department" id="transfer_department">
                                    <option value="">请选择划账部门</option>
                                </select>
                            </li>
                            <li>
                                <label>账单状态：</label>
                                <select name="status" id="status">
                                    <option value="">请选择账单状态</option>
                                    <option value="0">已入库</option>
                                    <option value="1">已发布</option>
                                    <option value="2">已确认</option>
                                    <option value="3">已退回</option>
                                </select>
                            </li>
                            <li><label>账单上传人：</label><input type="text" name="bill_publisher"/></li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-primary" onclick="publishBill()" shiro:hasPermission="expense:bill_manage:publish">
                    <i class="fa fa-upload"></i> 发布账单
                </a>
                <a class="btn btn-warning" onclick="revokeBill()" shiro:hasPermission="expense:bill_manage:revoke">
                    <i class="fa fa-undo"></i> 撤回账单
                </a>
                <a class="btn btn-danger" onclick="deleteBill()" shiro:hasPermission="expense:bill_manage:delete">
                    <i class="fa fa-trash"></i> 删除账单
                </a>

                <a class="btn btn-info" onclick="generateAllocationTable()" >
                    <i class="fa fa-file-excel-o" style="color: #ffffff;"></i> 生成分摊表
                </a>
                <a class="btn btn-primary" onclick="notifyAllBills()" shiro:hasPermission="expense:bill_manage:notify">
                    <i class="fa fa-bell" style="color: #ffffff;"></i> 通知对账
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        /*<![CDATA[*/
        // 使用若依框架的权限控制机制
        var notifyFlag = /*[[${@permission.hasPermi('expense:bill_manage:notify')}]]*/ false;
        var publishFlag = /*[[${@permission.hasPermi('expense:bill_manage:publish')}]]*/ false;
        var revokeFlag = /*[[${@permission.hasPermi('expense:bill_manage:revoke')}]]*/ false;
        var currentUser = /*[[${currentUser}]]*/ '';
        var prefix = ctx + "expense/bill_manage";
        /*]]>*/
        $(function() {
            var options = {
                url: prefix + "/list",
                modalName: "账单",
                pagination: true,
                pageSize: 50,
                pageList: [10, 25, 50, 100],
                showPageGo: true,
                showRefresh: true,
                showColumns: true,
                columns: [
                    { checkbox: true },
                    { field: 'expense_type', title: '费用类型' },
                    { field: 'billing_cycle', title: '计费周期' },
                    { field: 'transfer_department', title: '划账部门' },
                    { field: 'status', title: '账单状态', formatter: statusFormat },
                    { field: 'total_price_with_tax', title: '含税总价', formatter: priceFormatter },
                    { field: 'total_price_without_tax', title: '不含税总价', formatter: priceFormatter },
                    { field: 'bill_publisher', title: '账单上传人' },
                    { field: 'bill_confirmer', title: '账单确认人' },
                    { field: 'bill_returner', title: '账单退回人' },
                    { field: 'bill_refuse_comment', title: '账单退回意见' },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function(value, row, index) {
                            var actions = [];
                            actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="viewBill(' + row.bill_id + ')"><i class="fa fa-eye"></i>在线查看</a> ');
                            actions.push('<a class="btn btn-primary btn-xs" href="javascript:void(0)" onclick="exportBill(' + row.bill_id + ')"><i class="fa fa-download"></i>导出明细</a> ');
                            // 使用若依权限标志控制按钮显示
                            if (row.status == '0' && publishFlag == '') {
                                actions.push('<a class="btn btn-primary btn-xs" href="javascript:void(0)" onclick="publishBill(' + row.bill_id + ')"><i class="fa fa-upload"></i>发布</a> ');
                            }
                            if ((row.status == '0' || row.status == '1') && revokeFlag == '') {
                                actions.push('<a class="btn btn-warning btn-xs" href="javascript:void(0)" onclick="revokeBill(' + row.bill_id + ')"><i class="fa fa-undo"></i>撤回</a> ');
                            }
                            // 通知对账按钮：只对已发布状态的账单显示，且用户有通知权限
                            if (row.status == '1' && notifyFlag == '') {
                                actions.push('<a class="btn btn-warning btn-xs" href="javascript:void(0)" onclick="notifySingleBill(' + row.bill_id + ')"><i class="fa fa-bell"></i>通知对账</a> ');
                            }
                            return actions.join('');
                        }
                    }
                ],
                rowStyle: function(row, index) {
                    if (row.status == '0') {
                        return {classes: 'table-imported'};
                    } else if (row.status == '1') {
                        return {classes: 'table-released'};
                    } else if (row.status == '2') {
                        return {classes: 'table-confirmed'};
                    } else if (row.status == '3') {
                        return {classes: 'table-returned'};
                    }
                    return {};
                }
            };
            $.table.init(options);
            
            // 初始化下拉框数据
            initSelectOptions();
        });
        function statusFormat(value) {
            switch(value) {
                case '0': return '已入库';
                case '1': return '已发布';
                case '2': return '已确认';
                case '3': return '已退回';
                default: return value;
            }
        }
        function priceFormatter(value) {
            if (value == null || value == undefined || value == '') {
                return '0.00';
            }
            // 保留两位小数
            return parseFloat(value).toFixed(2);
        }
        function viewBill(id) {
            $.modal.openOptions({
                title: '账单详情',
                url: prefix + '/detail/' + id,
                width: 1200,
                height: 600,
                btn: ['关闭'],
                yes: function(index) {
                    layer.close(index);
                }
            });
        }
        function publishBill(id) {
            var ids = id ? [id] : $.table.selectColumns('bill_id');
            if (ids.length == 0) { $.modal.msgWarning('请选择要发布的账单'); return; }
            $.modal.confirm('确定要发布选中的账单吗？', function() {
                $.operate.post(prefix + '/publish', { billIds: ids });
            });
        }
        function revokeBill(id) {
            var ids = id ? [id] : $.table.selectColumns('bill_id');
            if (ids.length == 0) { $.modal.msgWarning('请选择要撤回的账单'); return; }
            $.modal.confirm('确定要撤回选中的账单吗？', function() {
                $.operate.post(prefix + '/revoke', { billIds: ids });
            });
        }
        function deleteBill(id) {
            var ids = id ? [id] : $.table.selectColumns('bill_id');
            if (ids.length == 0) { $.modal.msgWarning('请选择要删除的账单'); return; }
            $.modal.confirm('确定要删除选中的账单吗？<br/>删除后将无法恢复！', function() {
                $.operate.post(prefix + '/delete', { billIds: ids });
            });
        }
        function exportBill(id) {
            $.modal.confirm("是否导出该账单的费用明细？", function() {
                var url = ctx + "expense/bill_verify/export/" + id;
                window.open(url, "_blank");
            });
        }

        
        // 批量通知对账（通知所有已发布状态的账单）
        function notifyAllBills() {
            $.modal.confirm("确定要通知所有已发布状态的账单进行对账吗？", function() {
                $.operate.post(prefix + '/notifyAll');
            });
        }
        
        // 单独通知对账（通知指定的账单）
        function notifySingleBill(billId) {
            $.modal.confirm("确定要通知该账单进行对账吗？", function() {
                $.operate.post(prefix + '/notifySingle', { billId: billId });
            });
        }
        
        // 初始化下拉框选项
        function initSelectOptions() {
            // 加载费用类型字典
            loadDictOptions('expense_type', '#expense_type');
            
            // 加载划账部门字典
            loadDictOptions('expense_institude', '#transfer_department');
        }
        
        // 加载字典数据并填充下拉框
        function loadDictOptions(dictType, selectId) {
            $.ajax({
                url: prefix + '/getDictData',
                type: 'POST',
                data: {
                    dictType: dictType
                },
                success: function(result) {
                    if (result.code === 0 && result.data) {
                        var options = '<option value="">请选择</option>';
                        $.each(result.data, function(index, item) {
                            options += '<option value="' + item.dictValue + '">' + item.dictLabel + '</option>';
                        });
                        $(selectId).html(options);
                    } else {
                        console.error('加载字典数据失败：' + (result.msg || '未知错误'));
                    }
                },
                error: function(xhr, status, error) {
                    console.error('加载字典数据异常：' + error);
                }
            });
        }

        // 生成分摊表
        function generateAllocationTable() {
            // 弹出参数填写窗口
            $.modal.open("生成分摊表", prefix + '/generateAllocationTableDialog', 500, 350);
        }
    </script>
    <style>
        .table-imported { background-color: #ffffff !important; }
        .table-released { background-color: #fffbcd !important; }
        .table-confirmed { background-color: #d4edda !important; }
        .table-returned { background-color: #f8d7da !important; }
    </style>
</body>
</html> 
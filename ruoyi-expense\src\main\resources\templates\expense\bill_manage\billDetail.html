<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('账单详情')" />
    <link th:href="@{/ajax/libs/bootstrap-table/bootstrap-table.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/bill_detail.css}" rel="stylesheet"/>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="row">
            <div class="col-sm-12">
                <div class="bill-detail-container">
                    <div class="bill-detail-header">
                        <h5><i class="fa fa-file-text-o"></i>账单基本信息</h5>
                    </div>
                    <div class="bill-detail-content">
                        <!-- 基本信息行 -->
                        <div class="info-row basic-info">
                            <div class="info-item quarter-width">
                                <span class="info-label"><i class="fa fa-tags info-icon"></i>费用类型</span>
                                <span class="info-value" th:text="${bill.expense_type}"></span>
                            </div>
                            <div class="info-item quarter-width">
                                <span class="info-label"><i class="fa fa-calendar info-icon"></i>计费周期</span>
                                <span class="info-value" th:text="${bill.billing_cycle}"></span>
                            </div>
                            <div class="info-item quarter-width">
                                <span class="info-label"><i class="fa fa-building info-icon"></i>划账部门</span>
                                <span class="info-value" th:text="${bill.transfer_department}"></span>
                            </div>
                            <div class="info-item quarter-width">
                                <span class="info-label"><i class="fa fa-flag info-icon"></i>账单状态</span>
                                <span class="info-value">
                                    <span th:switch="${bill.status}">
                                        <span th:case="'0'" class="status-badge status-imported">已入库</span>
                                        <span th:case="'1'" class="status-badge status-published">已发布</span>
                                        <span th:case="'2'" class="status-badge status-confirmed">已确认</span>
                                        <span th:case="'3'" class="status-badge status-returned">已退回</span>
                                        <span th:case="*" class="status-badge status-unknown" th:text="${bill.status}"></span>
                                    </span>
                                </span>
                            </div>
                        </div>

                        <!-- 人员信息行 -->
                        <!-- <div class="info-row person-info">
                            <div class="info-item third-width">
                                <span class="info-label"><i class="fa fa-user info-icon"></i>账单发布人</span>
                                <span class="info-value" th:text="${bill.bill_publisher ?: '未设置'}"></span>
                            </div>
                            <div class="info-item third-width">
                                <span class="info-label"><i class="fa fa-user-check info-icon"></i>账单确认人</span>
                                <span class="info-value" th:text="${bill.bill_confirmer ?: '未确认'}"></span>
                            </div>
                            <div class="info-item third-width">
                                <span class="info-label"><i class="fa fa-user-times info-icon"></i>账单退回人</span>
                                <span class="info-value" th:text="${bill.bill_returner ?: '无'}"></span>
                            </div>
                        </div> -->

                        <!-- 价格信息行 -->
                        <div class="info-row price-info">
                            <div class="info-item half-width">
                                <span class="info-label"><i class="fa fa-money info-icon"></i>含税总价</span>
                                <span class="info-value price-value" th:text="${#numbers.formatDecimal(bill.total_price_with_tax, 1, 2)} + ' 元'"></span>
                            </div>
                            <div class="info-item half-width">
                                <span class="info-label"><i class="fa fa-calculator info-icon"></i>不含税总价</span>
                                <span class="info-value price-value" th:text="${#numbers.formatDecimal(bill.total_price_without_tax, 1, 2)} + ' 元'"></span>
                            </div>
                        </div>

                        <!-- 退回意见（条件显示） -->
                        <div class="comments-section" th:if="${bill.bill_refuse_comment != null && bill.bill_refuse_comment != ''}">
                            <div class="info-item full-width">
                                <span class="info-label"><i class="fa fa-comment info-icon"></i>退回意见</span>
                                <span class="info-value" th:text="${bill.bill_refuse_comment}"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>费用明细</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="table-responsive" style="max-height:500px;overflow:auto;">
                            <table class="table table-bordered table-striped table-fixed-header">
                                <thead>
                                    <tr>
                                        <th>名称</th>
                                        <th>编号</th>
                                        <th>品牌</th>
                                        <th>具体规格</th>
                                        <th>费用类型</th>
                                        <th>计费周期</th>
                                        <th>划账部门</th>
                                        <th>数量</th>
                                        <th>费用变动情况</th>
                                        <th>备注</th>
                                        <th>含税单价</th>
                                        <th>不含税单价</th>
                                        <th>含税单行总价</th>
                                        <th>不含税单行总价</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="detail : ${detailsList}">
                                        <td th:text="${detail.name}"></td>
                                        <td th:text="${detail.number}"></td>
                                        <td th:text="${detail.brand}"></td>
                                        <td th:text="${detail.specificSpecification}"></td>
                                        <td th:text="${detail.expenseType}"></td>
                                        <td th:text="${detail.billingCycle}"></td>
                                        <td th:text="${detail.transferDepartment}"></td>
                                        <td th:text="${detail.quantity}"></td>
                                        <td th:text="${detail.expenseChangeStatus}"></td>
                                        <td th:text="${detail.remarks}"></td>
                                        <td class="price-cell" th:text="${#numbers.formatDecimal(detail.unitPriceIncludingTax, 1, 2)}"></td>
                                        <td class="price-cell" th:text="${#numbers.formatDecimal(detail.unitPriceExcludingTax, 1, 2)}"></td>
                                        <td class="price-cell" th:text="${#numbers.formatDecimal(detail.totalLinePriceIncludingTax, 1, 2)}"></td>
                                        <td class="price-cell" th:text="${#numbers.formatDecimal(detail.totalLinePriceExcludingTax, 1, 2)}"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
</body>
</html>